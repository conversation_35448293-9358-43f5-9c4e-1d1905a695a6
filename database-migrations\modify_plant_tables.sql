-- 修改多肉品种管理和等级系统表结构
-- 执行日期: 2025-08-27
-- 说明: 根据需求修改plant_species和plant_level_config表结构

-- 开始事务
START TRANSACTION;

-- ========================================
-- 1. 备份现有数据
-- ========================================

-- 备份plant_species表
DROP TABLE IF EXISTS plant_species_backup;
CREATE TABLE plant_species_backup AS SELECT * FROM plant_species;

-- 备份plant_level_config表  
DROP TABLE IF EXISTS plant_level_config_backup;
CREATE TABLE plant_level_config_backup AS SELECT * FROM plant_level_config;

-- 验证备份
SELECT 
    'plant_species备份验证' as step,
    COUNT(*) as backup_count,
    (SELECT COUNT(*) FROM plant_species) as original_count
FROM plant_species_backup;

SELECT 
    'plant_level_config备份验证' as step,
    COUNT(*) as backup_count,
    (SELECT COUNT(*) FROM plant_level_config) as original_count
FROM plant_level_config_backup;

-- ========================================
-- 2. 修改plant_species表结构
-- ========================================

-- 删除不需要的字段
ALTER TABLE plant_species DROP COLUMN unlock_condition;
ALTER TABLE plant_species DROP COLUMN max_level;
ALTER TABLE plant_species DROP COLUMN base_energy_per_level;

-- 添加新字段
ALTER TABLE plant_species ADD COLUMN unlock_level INT DEFAULT 1 COMMENT '解锁等级' AFTER rarity;
ALTER TABLE plant_species ADD COLUMN need_vip BOOLEAN DEFAULT FALSE COMMENT '是否需要VIP' AFTER unlock_level;

-- ========================================
-- 3. 修改plant_level_config表结构
-- ========================================

-- 删除不需要的字段
ALTER TABLE plant_level_config DROP COLUMN attribute_bonus;
ALTER TABLE plant_level_config DROP COLUMN required_days;
ALTER TABLE plant_level_config DROP COLUMN special_ability;
ALTER TABLE plant_level_config DROP COLUMN description;

-- 重命名unlock_reward字段为coin_reward
ALTER TABLE plant_level_config CHANGE COLUMN unlock_reward coin_reward TEXT COMMENT '金币奖励';

-- ========================================
-- 4. 验证修改结果
-- ========================================

-- 查看plant_species表结构
DESCRIBE plant_species;

-- 查看plant_level_config表结构  
DESCRIBE plant_level_config;

-- 验证数据完整性
SELECT 
    'plant_species数据验证' as step,
    COUNT(*) as current_count,
    (SELECT COUNT(*) FROM plant_species_backup) as backup_count
FROM plant_species;

SELECT 
    'plant_level_config数据验证' as step,
    COUNT(*) as current_count,
    (SELECT COUNT(*) FROM plant_level_config_backup) as backup_count
FROM plant_level_config;

-- ========================================
-- 5. 更新现有数据（如果需要）
-- ========================================

-- 为现有的plant_species记录设置默认值
UPDATE plant_species SET unlock_level = 1 WHERE unlock_level IS NULL;
UPDATE plant_species SET need_vip = FALSE WHERE need_vip IS NULL;

-- 为现有的plant_level_config记录更新coin_reward字段（如果原来有unlock_reward数据）
-- 这里假设原来的unlock_reward是JSON格式，包含coins字段
-- UPDATE plant_level_config SET coin_reward = JSON_EXTRACT(coin_reward, '$.coins') WHERE coin_reward IS NOT NULL;

-- 提交事务
COMMIT;

-- ========================================
-- 6. 清理备份表（可选，建议保留一段时间）
-- ========================================
-- 如果确认修改无误，可以删除备份表
-- DROP TABLE IF EXISTS plant_species_backup;
-- DROP TABLE IF EXISTS plant_level_config_backup;

SELECT '数据库表结构修改完成！' as result;
