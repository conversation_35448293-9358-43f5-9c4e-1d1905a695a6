import {
  System as SystemConfig
} from '../config.js'

// 截取字符串，多余的部分用...代替
export const setString = (str, len) => {
  let StrLen = 0
  let s = ''
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 128) {
      StrLen += 2
    } else {
      StrLen++
    }
    s += str.charAt(i)
    if (StrLen >= len) {
      return s + '...'
    }
  }
  return s
}

// 格式化设置
export const OptionFormat = (GetOptions) => {
  let options = '{'
  for (let n = 0; n < GetOptions.length; n++) {
    options = options + '\'' + GetOptions[n].option_name + '\':\'' + GetOptions[n].option_value + '\''
    if (n < GetOptions.length - 1) {
      options = options + ','
    }
  }
  return JSON.parse(options + '}')
}

// 替换SQL字符串中的前缀
export const SqlFormat = (str) => {
  if (SystemConfig.mysql_prefix !== 'api_') {
    str = str.replace(/api_/g, SystemConfig.mysql_prefix)
  }
  return str
}

// 数组去重
export const HovercUnique = (arr) => {
  const n = {}
  const r = []
  for (var i = 0; i < arr.length; i++) {
    if (!n[arr[i]]) {
      n[arr[i]] = true
      r.push(arr[i])
    }
  }
  return r
}

// 获取json长度
export const getJsonLength = (jsonData) => {
  var arr = []
  for (var item in jsonData) {
    arr.push(jsonData[item])
  }
  return arr.length
}

// 分页参数处理
export const parsePaginationParams = (query) => {
  const pageNum = parseInt(query.pageNum || query.page || 1)
  const pageSize = parseInt(query.pageSize || query.limit || 10)
  const offset = (pageNum - 1) * pageSize

  return {
    pageNum,
    pageSize,
    offset
  }
}

// 分页响应格式化
export const formatPaginationResponse = (data, total, pageNum, pageSize) => {
  const pages = Math.ceil(total / pageSize)

  return {
    total,
    pageNum,
    pageSize,
    pages,
    items: data
  }
}

/**
 * 计算用户连续坚持天数
 * @param {number} userId - 用户ID
 * @returns {Promise<number>} 连续天数
 */
export const calculateUserStreakDays = async (userId) => {
  const { UserMeditationStats } = await import('../models/associations.js')
  const moment = (await import('moment')).default

  try {
    // 获取用户所有的日统计记录，按日期倒序排列
    const stats = await UserMeditationStats.findAll({
      where: {
        user_id: userId,
        period_type: 'day'
      },
      order: [['period_date', 'DESC']]
    })

    if (!stats || stats.length === 0) {
      return 0
    }

    let streakDays = 0
    const today = moment().format('YYYY-MM-DD')
    const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD')

    // 检查今天或昨天是否有记录（允许今天还没有记录的情况）
    const latestRecord = stats[0]
    const latestDate = moment(latestRecord.period_date).format('YYYY-MM-DD')

    // 如果最新记录不是今天也不是昨天，说明连续记录已断
    if (latestDate !== today && latestDate !== yesterday) {
      return 0
    }

    // 从最新记录开始，向前计算连续天数
    let currentDate = moment(latestRecord.period_date)

    for (let i = 0; i < stats.length; i++) {
      const recordDate = moment(stats[i].period_date)
      const expectedDate = currentDate.clone().subtract(i, 'day')

      // 检查是否是连续的日期
      if (recordDate.format('YYYY-MM-DD') === expectedDate.format('YYYY-MM-DD')) {
        // 检查这一天是否有实际的冥想活动（任务完成数 > 0 或冥想时长 > 0）
        if (stats[i].tasks_completed > 0 || stats[i].meditation_duration > 0) {
          streakDays++
        } else {
          // 如果这一天没有实际活动，连续记录断开
          break
        }
      } else {
        // 日期不连续，连续记录断开
        break
      }
    }

    return streakDays
  } catch (error) {
    console.error('计算连续天数失败:', error)
    return 0
  }
}

/**
 * 更新用户连续坚持天数
 * @param {number} userId - 用户ID
 * @returns {Promise<number>} 更新后的连续天数
 */
export const updateUserStreakDays = async (userId) => {
  const { User } = await import('../models/associations.js')

  try {
    const streakDays = await calculateUserStreakDays(userId)

    // 更新用户表中的streak_days字段
    await User.update(
      {
        streak_days: streakDays,
        updated_at: new Date()
      },
      {
        where: { id: userId }
      }
    )

    return streakDays
  } catch (error) {
    console.error('更新连续天数失败:', error)
    throw error
  }
}
