import sequelizePkg from 'sequelize'
import sequelize from '../lib/sequelize.js'

const { DataTypes } = sequelizePkg

const PlantSpecies = sequelize.define('PlantSpecies', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
    comment: '品种名称'
  },
  display_name: {
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '显示名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '品种描述'
  },
  rarity: {
    type: DataTypes.ENUM('common', 'rare', 'epic', 'legendary'),
    defaultValue: 'common',
    comment: '稀有度：普通/稀有/史诗/传说'
  },
  unlock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '解锁等级'
  },
  need_vip: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否需要VIP'
  },
  growth_stages: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '成长阶段配置（JSON格式）'
  },
  image_urls: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '各阶段图片URL（JSON格式）'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'plant_species',
  timestamps: false,
  comment: '多肉品种配置表'
})

export default PlantSpecies
