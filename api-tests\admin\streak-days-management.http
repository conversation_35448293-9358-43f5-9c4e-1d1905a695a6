### 连续天数管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

@baseUrl = http://localhost:3004
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MjcyMDUyLCJleHAiOjE3NTYzNTg0NTJ9.vaSF1IbTK4gcUVk24c5zzYHiPqMtUX1Z2IU_5O4nNNc
@testUserId = 1

### 1. 批量更新所有用户连续天数
POST {{baseUrl}}/admin/users/update-streak-days
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### 2. 更新指定用户连续天数
POST {{baseUrl}}/admin/users/{{testUserId}}/update-streak-days
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### 3. 获取用户详情（验证连续天数是否更新）
GET {{baseUrl}}/admin/users/{{testUserId}}
Authorization: Bearer {{adminToken}}

### 4. 获取用户统计（查看整体连续天数统计）
GET {{baseUrl}}/admin/users/statistics
Authorization: Bearer {{adminToken}}
