# 标签管理接口测试说明

## 概述
`tag-management.http` 文件包含了管理员标签管理功能的完整HTTP接口测试用例。

## 测试覆盖的功能
1. **标签列表查询**
   - 分页查询
   - 搜索功能
   - 边界条件测试

2. **标签创建**
   - 正常创建
   - 重复名称检查
   - 空值验证
   - 特殊字符处理

3. **标签更新**
   - 正常更新
   - 名称冲突检查
   - 不存在的标签处理

4. **标签删除**
   - 单个删除
   - 使用中标签的删除限制
   - 不存在标签的处理

5. **批量删除**
   - 正常批量删除
   - 使用中标签的过滤
   - 参数验证

6. **权限验证**
   - 无token访问
   - 无效token访问

## 使用方法

### 前置条件
1. 确保服务器已启动
2. 确保数据库连接正常
3. 确保有管理员账户可用于测试

### 执行步骤
1. 首先执行 `../auth/admin-auth.http` 获取管理员token
2. 按顺序执行测试用例（某些测试依赖前面创建的数据）
3. 观察返回结果，验证接口行为是否符合预期

### 测试用例分类

#### 基础功能测试 (1-8)
- 标签列表的各种查询方式
- 分页参数的边界测试

#### 创建功能测试 (9-18)
- 正常创建和异常情况处理
- 数据验证和重复检查

#### 更新功能测试 (19-24)
- 正常更新和各种异常情况

#### 删除功能测试 (25-32)
- 单个删除和批量删除
- 使用中标签的保护机制

#### 权限测试 (33-37)
- 认证和授权验证

#### 边界条件测试 (38-40)
- 极限参数测试

#### 扩展功能测试 (41-55)
- 标签使用情况测试
- 数据验证测试
- 性能测试
- 搜索功能详细测试
- 错误处理测试

## 注意事项

1. **测试顺序**：某些测试用例依赖前面创建的数据，建议按顺序执行

2. **数据清理**：测试完成后可能需要清理测试数据

3. **ID替换**：测试中的标签ID（如 `/admin/meditation/tags/1`）需要根据实际创建的标签ID进行调整

4. **使用中标签测试**：测试用例41和42需要先有冥想内容使用了相应的标签才能正确测试删除限制功能

5. **参数名称**：批量删除接口使用 `tag_ids` 参数名（不是 `tagIds`）

## 预期结果

### 成功情况
- 创建标签：返回201状态码和标签信息
- 查询标签：返回200状态码和分页数据
- 更新标签：返回200状态码和更新后的标签信息
- 删除标签：返回200状态码和成功消息

### 失败情况
- 重复标签名：返回400状态码和错误消息
- 标签不存在：返回404状态码和错误消息
- 删除使用中标签：返回400状态码和使用情况说明
- 无权限访问：返回401状态码

## 相关文件
- 控制器：`src/controllers/adminTag.js`
- 模型：`src/models/MeditationTag.js`
- 路由：`src/routes/admin-routes.js`（第78-84行）
