import swaggerJSDoc from 'swagger-jsdoc'
import koa2SwaggerUI from 'koa2-swagger-ui'
import { Swagger } from '../config.js'
import path from 'path'
import { fileURLToPath } from 'url'

// 在ES模块中获取__dirname的等价物
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const { koaSwagger } = koa2SwaggerUI

// Swagger 配置选项
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: Swagger.title,
      version: Swagger.version,
      description: Swagger.description,
    },
    servers: [
      {
        url: `http://${Swagger.host}${Swagger.basePath}`,
        description: '开发环境'
      }
    ],
    components: {
      securitySchemes: {
        Bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token, 格式: Bearer <token>'
        }
      },
      schemas: {
        CourseChaptersResponse: {
          type: 'object',
          properties: {
            course: {
              type: 'object',
              properties: {
                id: {
                  type: 'integer',
                  description: '课程ID'
                },
                title: {
                  type: 'string',
                  description: '课程标题'
                },
                description: {
                  type: 'string',
                  description: '课程描述'
                }
              }
            },
            chapters: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/MeditationContent'
              },
              description: '章节列表'
            }
          }
        }
      }
    }
  },
  apis: [
    path.join(__dirname, '../controllers/*.js'),
    path.join(__dirname, '../routes/*.js')
  ]
}

// 生成 Swagger 规范
const swaggerSpec = swaggerJSDoc(swaggerOptions)

// Swagger UI 中间件
export const swaggerUI = koaSwagger({
  routePrefix: '/swagger',
  swaggerOptions: {
    spec: swaggerSpec,
  },
})

// Swagger JSON 中间件
export const swaggerJSON = (ctx) => {
  ctx.set('Content-Type', 'application/json')
  ctx.body = swaggerSpec
}