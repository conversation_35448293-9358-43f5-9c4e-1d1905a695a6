/**
 * 批量更新所有用户的连续天数脚本
 * 可以通过定时任务或手动执行来更新所有用户的streak_days字段
 */

import { User } from '../models/associations.js'
import { updateUserStreakDays } from '../tool/Common.js'

/**
 * 批量更新所有用户的连续天数
 */
async function updateAllUsersStreakDays() {
  console.log('开始批量更新用户连续天数...')
  
  try {
    // 获取所有用户
    const users = await User.findAll({
      attributes: ['id', 'nickname', 'streak_days'],
      order: [['id', 'ASC']]
    })

    console.log(`找到 ${users.length} 个用户，开始更新...`)

    let successCount = 0
    let errorCount = 0

    // 逐个更新用户的连续天数
    for (const user of users) {
      try {
        const oldStreakDays = user.streak_days
        const newStreakDays = await updateUserStreakDays(user.id)
        
        if (oldStreakDays !== newStreakDays) {
          console.log(`用户 ${user.id} (${user.nickname || '未设置昵称'}) 连续天数: ${oldStreakDays} -> ${newStreakDays}`)
        }
        
        successCount++
      } catch (error) {
        console.error(`更新用户 ${user.id} 连续天数失败:`, error.message)
        errorCount++
      }
    }

    console.log(`批量更新完成！成功: ${successCount}, 失败: ${errorCount}`)
    
    return {
      total: users.length,
      success: successCount,
      error: errorCount
    }
  } catch (error) {
    console.error('批量更新用户连续天数失败:', error)
    throw error
  }
}

/**
 * 更新指定用户的连续天数
 * @param {number} userId - 用户ID
 */
async function updateSingleUserStreakDays(userId) {
  console.log(`开始更新用户 ${userId} 的连续天数...`)
  
  try {
    const user = await User.findByPk(userId, {
      attributes: ['id', 'nickname', 'streak_days']
    })

    if (!user) {
      console.error(`用户 ${userId} 不存在`)
      return null
    }

    const oldStreakDays = user.streak_days
    const newStreakDays = await updateUserStreakDays(userId)
    
    console.log(`用户 ${userId} (${user.nickname || '未设置昵称'}) 连续天数: ${oldStreakDays} -> ${newStreakDays}`)
    
    return {
      userId,
      oldStreakDays,
      newStreakDays,
      changed: oldStreakDays !== newStreakDays
    }
  } catch (error) {
    console.error(`更新用户 ${userId} 连续天数失败:`, error)
    throw error
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  
  if (args.length > 0 && args[0] === '--user') {
    // 更新指定用户
    const userId = parseInt(args[1])
    if (!userId || isNaN(userId)) {
      console.error('请提供有效的用户ID')
      process.exit(1)
    }
    
    updateSingleUserStreakDays(userId)
      .then((result) => {
        if (result) {
          console.log('更新完成:', result)
        }
        process.exit(0)
      })
      .catch((error) => {
        console.error('更新失败:', error)
        process.exit(1)
      })
  } else {
    // 更新所有用户
    updateAllUsersStreakDays()
      .then((result) => {
        console.log('批量更新结果:', result)
        process.exit(0)
      })
      .catch((error) => {
        console.error('批量更新失败:', error)
        process.exit(1)
      })
  }
}

export { updateAllUsersStreakDays, updateSingleUserStreakDays }
