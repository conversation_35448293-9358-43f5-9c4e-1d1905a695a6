### 用户管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

@baseUrl = http://localhost:3004
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MjcyMDUyLCJleHAiOjE3NTYzNTg0NTJ9.vaSF1IbTK4gcUVk24c5zzYHiPqMtUX1Z2IU_5O4nNNc
@testUserId = 1

### 1. 获取用户列表 - 默认分页
GET {{baseUrl}}/admin/users
Authorization: Bearer {{adminToken}}

### 2. 获取用户列表 - 指定分页
GET {{baseUrl}}/admin/users?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 3. 获取用户列表 - 搜索用户
GET {{baseUrl}}/admin/users?search=测试&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 获取用户列表 - 按等级筛选
GET {{baseUrl}}/admin/users?level=2&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取用户列表 - 按状态筛选
GET {{baseUrl}}/admin/users?status=active&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取用户列表 - 按注册时间排序
GET {{baseUrl}}/admin/users?sort=created_at&order=desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 7. 获取用户列表 - 按最后登录时间排序
GET {{baseUrl}}/admin/users?sort=last_login&order=desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 8. 获取用户列表 - 复合筛选
GET {{baseUrl}}/admin/users?search=用户&level=1&status=active&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 9. 获取用户统计数据
GET {{baseUrl}}/admin/users/statistics
Authorization: Bearer {{adminToken}}

### 10. 获取用户统计数据 - 按时间范围
GET {{baseUrl}}/admin/users/statistics?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{adminToken}}

### 11. 获取用户统计数据 - 按等级分组
GET {{baseUrl}}/admin/users/statistics?group_by=level
Authorization: Bearer {{adminToken}}

### 12. 获取用户详情 - 正常用户
GET {{baseUrl}}/admin/users/{{testUserId}}
Authorization: Bearer {{adminToken}}

### 13. 获取用户详情 - 不存在的用户
GET {{baseUrl}}/admin/users/99999
Authorization: Bearer {{adminToken}}

### 14. 获取用户详情 - 无效用户ID
GET {{baseUrl}}/admin/users/invalid_id
Authorization: Bearer {{adminToken}}

### 15. 更新用户等级 - 正常更新
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 3,
  "reason": "管理员手动调整等级"
}

### 16. 更新用户等级 - 升级到最高等级
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 5,
  "reason": "升级到最高等级"
}

### 17. 更新用户等级 - 降级
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 1,
  "reason": "降级处理"
}

### 18. 更新用户等级 - 无效等级
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 0,
  "reason": "测试无效等级"
}

### 19. 更新用户等级 - 超出范围的等级
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 100,
  "reason": "测试超出范围等级"
}

### 20. 更新用户等级 - 缺少原因
PUT {{baseUrl}}/admin/users/{{testUserId}}/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 2
}

### 21. 更新用户等级 - 不存在的用户
PUT {{baseUrl}}/admin/users/99999/level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 2,
  "reason": "测试不存在用户"
}

### 22. 批量查询用户详情
GET {{baseUrl}}/admin/users/1
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/admin/users/2
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/admin/users/3
Authorization: Bearer {{adminToken}}

### 23. 用户搜索测试 - 按昵称搜索
GET {{baseUrl}}/admin/users?search=测试用户&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 24. 用户搜索测试 - 按openid搜索
GET {{baseUrl}}/admin/users?search={{testUserOpenId}}&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 25. 用户搜索测试 - 模糊搜索
GET {{baseUrl}}/admin/users?search=用户&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 26. 分页边界测试 - 第一页
GET {{baseUrl}}/admin/users?page=1&limit=1
Authorization: Bearer {{adminToken}}

### 27. 分页边界测试 - 超出范围的页码
GET {{baseUrl}}/admin/users?page=99999&limit=10
Authorization: Bearer {{adminToken}}

### 28. 分页边界测试 - 无效的limit
GET {{baseUrl}}/admin/users?page=1&limit=0
Authorization: Bearer {{adminToken}}

### 29. 分页边界测试 - 过大的limit
GET {{baseUrl}}/admin/users?page=1&limit=1000
Authorization: Bearer {{adminToken}}

### 30. 权限测试 - 无token访问
GET {{baseUrl}}/admin/users

### 31. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/users
Authorization: Bearer invalid_token

### 32. 权限测试 - 普通管理员权限
# 注意：需要普通管理员token
GET {{baseUrl}}/admin/users
Authorization: Bearer normal_admin_token_here

### 33. 性能测试 - 大量数据查询
GET {{baseUrl}}/admin/users?page=1&limit=100
Authorization: Bearer {{adminToken}}

### 34. 统计数据详细测试 - 活跃用户统计
GET {{baseUrl}}/admin/users/statistics?type=active_users&days=30
Authorization: Bearer {{adminToken}}

### 35. 统计数据详细测试 - 新用户统计
GET {{baseUrl}}/admin/users/statistics?type=new_users&days=7
Authorization: Bearer {{adminToken}}

### 36. 用户状态管理测试 - 查看不同状态用户
GET {{baseUrl}}/admin/users?status=inactive&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 37. 用户等级分布测试
GET {{baseUrl}}/admin/users/statistics?group_by=level&include_details=true
Authorization: Bearer {{adminToken}}

### 38. 用户活跃度测试 - 按最后登录时间筛选
GET {{baseUrl}}/admin/users?last_login_after=2024-01-01&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 39. 用户成长数据测试 - 查看用户成长轨迹
GET {{baseUrl}}/admin/users/{{testUserId}}?include_growth_history=true
Authorization: Bearer {{adminToken}}

### 40. 用户多肉数据测试 - 查看用户多肉信息
GET {{baseUrl}}/admin/users/{{testUserId}}?include_plants=true
Authorization: Bearer {{adminToken}}
