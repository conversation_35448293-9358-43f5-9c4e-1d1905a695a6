import sequelizePkg from 'sequelize'
import sequelize from '../lib/sequelize.js'

const { DataTypes } = sequelizePkg

const PlantLevelConfig = sequelize.define('PlantLevelConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    comment: '等级'
  },
  name: {
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '等级名称（幼苗、小苗、成株、精品、传奇）'
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '等级图标URL'
  },
  required_energy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '所需能量'
  },
  coin_reward: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '金币奖励'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序顺序'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'plant_level_config',
  timestamps: false,
  comment: '多肉等级配置表'
})

export default PlantLevelConfig
