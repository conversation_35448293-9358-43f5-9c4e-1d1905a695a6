import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const MeditationTag = sequelize.define('meditation_tags', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: Sequelize.STRING(64),
    allowNull: false,
    unique: true
  },
  is_enabled: {
    type: Sequelize.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'meditation_tags'
})

export default MeditationTag