### 冥想内容接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取用户token
< ../auth/user-auth.http

@apiUrl = http://localhost:3004
@testUserToken =  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MjcyMDUyLCJleHAiOjE3NTYzNTg0NTJ9.vaSF1IbTK4gcUVk24c5zzYHiPqMtUX1Z2IU_5O4nNNc
@testMeditationId = 25
@defaultPage = 1
@defaultLimit = 10
@testTagId = 1

### 1. 获取冥想内容列表 - 默认分页
GET {{apiUrl}}/meditation/list
Authorization: Bearer {{testUserToken}}

### 2. 获取冥想内容列表 - 指定分页
GET {{apiUrl}}/meditation/list?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{testUserToken}}

### 3. 获取冥想内容列表 - 按标签筛选
GET {{apiUrl}}/meditation/list?tag_id={{testTagId}}&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 4. 获取冥想内容列表 - 按难度筛选
GET {{apiUrl}}/meditation/list?difficulty=beginner&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 5. 获取冥想内容列表 - 按时长筛选
GET {{apiUrl}}/meditation/list?duration=10&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 6. 获取冥想内容列表 - 按时长范围筛选
GET {{apiUrl}}/meditation/list?min_duration=5&max_duration=15&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 7. 获取冥想内容列表 - 按类型筛选
GET {{apiUrl}}/meditation/list?type=guided&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 8. 获取冥想内容列表 - 按热度排序
GET {{apiUrl}}/meditation/list?sort=popularity&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 9. 获取冥想内容列表 - 按时间排序
GET {{apiUrl}}/meditation/list?sort=created_at&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 10. 获取冥想内容列表 - 按评分排序
GET {{apiUrl}}/meditation/list?sort=rating&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 11. 获取冥想内容列表 - 复合筛选
GET {{apiUrl}}/meditation/list?tag_id={{testTagId}}&difficulty=beginner&type=guided&sort=popularity&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 12. 获取冥想内容详情 - 正常内容
GET {{apiUrl}}/meditation/{{testMeditationId}}
Authorization: Bearer {{testUserToken}}

### 13. 获取冥想内容详情 - 不存在的内容
GET {{apiUrl}}/meditation/99999
Authorization: Bearer {{testUserToken}}

### 14. 获取冥想内容详情 - 包含相关推荐
GET {{apiUrl}}/meditation/{{testMeditationId}}?include_related=true
Authorization: Bearer {{testUserToken}}

### 15. 获取冥想内容详情 - 包含用户数据
GET {{apiUrl}}/meditation/{{testMeditationId}}?include_user_data=true
Authorization: Bearer {{testUserToken}}

### 16. 收藏冥想内容 - 添加收藏
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}

### 17. 收藏冥想内容 - 取消收藏
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}

### 18. 收藏冥想内容 - 重复收藏
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}

###
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}

### 19. 收藏冥想内容 - 不存在的内容
POST {{apiUrl}}/meditation/99999/favorite
Authorization: Bearer {{testUserToken}}

### 20. 搜索冥想内容 - 关键词搜索
GET {{apiUrl}}/meditation/search?q=冥想&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 21. 搜索冥想内容 - 标题搜索
GET {{apiUrl}}/meditation/search?q=放松&search_in=title&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 22. 搜索冥想内容 - 描述搜索
GET {{apiUrl}}/meditation/search?q=呼吸&search_in=description&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 23. 搜索冥想内容 - 全文搜索
GET {{apiUrl}}/meditation/search?q=正念&search_in=all&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 24. 搜索冥想内容 - 空搜索词
GET {{apiUrl}}/meditation/search?q=&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 25. 搜索冥想内容 - 特殊字符搜索
GET {{apiUrl}}/meditation/search?q=@#$%&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 26. 搜索冥想内容 - 长搜索词
GET {{apiUrl}}/meditation/search?q=很长很长的搜索词测试&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 27. 搜索冥想内容 - 结合筛选条件
GET {{apiUrl}}/meditation/search?q=冥想&difficulty=beginner&type=guided&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 28. 获取标签列表 - 所有标签
GET {{apiUrl}}/meditation/tags
Authorization: Bearer {{testUserToken}}

### 29. 获取标签列表 - 热门标签
GET {{apiUrl}}/meditation/tags?type=popular&limit=20
Authorization: Bearer {{testUserToken}}

### 30. 获取标签列表 - 按使用次数排序
GET {{apiUrl}}/meditation/tags?sort=usage_count&order=desc
Authorization: Bearer {{testUserToken}}

### 31. 权限测试 - 无token访问列表
GET {{apiUrl}}/meditation/list

### 32. 权限测试 - 无token访问详情
GET {{apiUrl}}/meditation/{{testMeditationId}}

### 33. 权限测试 - 无token收藏
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite

### 34. 权限测试 - 无token搜索
GET {{apiUrl}}/meditation/search?q=冥想

### 35. 权限测试 - 无token获取标签
GET {{apiUrl}}/meditation/tags

### 36. 权限测试 - 无效token访问
GET {{apiUrl}}/meditation/list
Authorization: Bearer invalid_token

### 37. 分页测试 - 第一页
GET {{apiUrl}}/meditation/list?page=1&limit=1
Authorization: Bearer {{testUserToken}}

### 38. 分页测试 - 超出范围页码
GET {{apiUrl}}/meditation/list?page=99999&limit=10
Authorization: Bearer {{testUserToken}}

### 39. 分页测试 - 无效limit
GET {{apiUrl}}/meditation/list?page=1&limit=0
Authorization: Bearer {{testUserToken}}

### 40. 分页测试 - 过大limit
GET {{apiUrl}}/meditation/list?page=1&limit=1000
Authorization: Bearer {{testUserToken}}

### 41. 参数测试 - 无效难度
GET {{apiUrl}}/meditation/list?difficulty=invalid_difficulty&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 42. 参数测试 - 无效类型
GET {{apiUrl}}/meditation/list?type=invalid_type&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 43. 参数测试 - 无效排序字段
GET {{apiUrl}}/meditation/list?sort=invalid_field&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 44. 参数测试 - 无效时长
GET {{apiUrl}}/meditation/list?duration=-1&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 45. 参数测试 - 时长范围错误
GET {{apiUrl}}/meditation/list?min_duration=20&max_duration=10&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 46. 性能测试 - 大量数据查询
GET {{apiUrl}}/meditation/list?page=1&limit=100
Authorization: Bearer {{testUserToken}}

### 47. 性能测试 - 复杂搜索
GET {{apiUrl}}/meditation/search?q=冥想&difficulty=beginner&type=guided&sort=popularity&order=desc&page=1&limit=50
Authorization: Bearer {{testUserToken}}

### 48. 缓存测试 - 重复请求列表
GET {{apiUrl}}/meditation/list?page=1&limit=10
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/meditation/list?page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 49. 缓存测试 - 重复请求详情
GET {{apiUrl}}/meditation/{{testMeditationId}}
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/meditation/{{testMeditationId}}
Authorization: Bearer {{testUserToken}}

### 50. 并发测试 - 同时收藏和取消收藏
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}

###
POST {{apiUrl}}/meditation/{{testMeditationId}}/favorite
Authorization: Bearer {{testUserToken}}
