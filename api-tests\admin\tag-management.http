### 标签管理接口测试
###
### 使用说明：
### 1. 确保已启动服务器
### 2. 先执行 ../auth/admin-auth.http 获取管理员token
### 3. 按顺序执行测试用例，某些测试依赖前面创建的数据
### 4. 测试用例包含正常流程、异常处理、边界条件等
###
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

### 本地变量定义（用于独立运行）
@baseUrl = http://localhost:3004
@adminToken =  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MjcyMDUyLCJleHAiOjE3NTYzNTg0NTJ9.vaSF1IbTK4gcUVk24c5zzYHiPqMtUX1Z2IU_5O4nNNc
@defaultPage = 1
@defaultLimit = 10
@maxLimit = 100

### ========================================
### 标签列表查询测试
### ========================================

### 1. 获取标签列表 - 默认分页
GET {{baseUrl}}/admin/meditation/tags?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 2. 获取标签列表 - 指定分页
GET {{baseUrl}}/admin/meditation/tags?page=1&limit=5
Authorization: Bearer {{adminToken}}

### 3. 获取标签列表 - 大分页测试
GET {{baseUrl}}/admin/meditation/tags?page=1&limit={{maxLimit}}
Authorization: Bearer {{adminToken}}

### 4. 获取标签列表 - 搜索功能
GET {{baseUrl}}/admin/meditation/tags?search=冥想&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取标签列表 - 搜索不存在的标签
GET {{baseUrl}}/admin/meditation/tags?search=不存在的标签&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取标签列表 - 空搜索参数
GET {{baseUrl}}/admin/meditation/tags?search=&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 7. 获取标签列表 - 无效分页参数
GET {{baseUrl}}/admin/meditation/tags?page=0&limit=0
Authorization: Bearer {{adminToken}}

### 8. 获取标签列表 - 负数分页参数
GET {{baseUrl}}/admin/meditation/tags?page=-1&limit=-5
Authorization: Bearer {{adminToken}}

### ========================================
### 标签创建测试
### ========================================

### 9. 创建标签 - 正常创建
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "测试标签"
}

### 10. 创建标签 - 创建另一个标签
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "放松冥想"
}

### 11. 创建标签 - 创建第三个标签
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "专注训练"
}

### 12. 创建标签 - 重复名称（应该失败）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "测试标签"
}

### 13. 创建标签 - 空名称（应该失败）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": ""
}

### 14. 创建标签 - 只有空格的名称（应该失败）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "   "
}

### 15. 创建标签 - 缺少name字段（应该失败）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{}

### 16. 创建标签 - 名称包含特殊字符
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "冥想&放松"
}

### 17. 创建标签 - 长名称测试
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "这是一个非常长的标签名称用来测试系统对长名称的处理能力"
}

### 18. 创建标签 - 包含前后空格（应该自动trim）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "  睡前冥想  "
}

### ========================================
### 标签更新测试
### ========================================

### 19. 更新标签 - 正常更新（需要先创建一个标签获取ID）
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "更新后的标签名称"
}

### 20. 更新标签 - 更新为已存在的名称（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "放松冥想"
}

### 21. 更新标签 - 空名称（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": ""
}

### 22. 更新标签 - 不存在的标签ID（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/99999
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "不存在的标签"
}

### 23. 更新标签 - 无效的标签ID（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/abc
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "无效ID测试"
}

### 24. 更新标签 - 缺少name字段（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{}

### ========================================
### 标签删除测试
### ========================================

### 25. 删除标签 - 正常删除
DELETE {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}

### 26. 删除标签 - 删除不存在的标签（应该失败）
DELETE {{baseUrl}}/admin/meditation/tags/99999
Authorization: Bearer {{adminToken}}

### 27. 删除标签 - 无效的标签ID（应该失败）
DELETE {{baseUrl}}/admin/meditation/tags/abc
Authorization: Bearer {{adminToken}}

### ========================================
### 批量删除标签测试
### ========================================

### 28. 批量删除标签 - 正常批量删除
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": [2, 3]
}

### 29. 批量删除标签 - 空数组（应该失败）
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": []
}

### 30. 批量删除标签 - 包含不存在的ID
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": [99999, 99998]
}

### 31. 批量删除标签 - 缺少tag_ids字段（应该失败）
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{}

### 32. 批量删除标签 - 无效的ID格式
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": ["abc", "def"]
}

### ========================================
### 权限测试
### ========================================

### 33. 无token访问 - 获取标签列表（应该失败）
GET {{baseUrl}}/admin/meditation/tags

### 34. 无token访问 - 创建标签（应该失败）
POST {{baseUrl}}/admin/meditation/tags
Content-Type: application/json

{
  "name": "无权限测试"
}

### 35. 无token访问 - 更新标签（应该失败）
PUT {{baseUrl}}/admin/meditation/tags/1
Content-Type: application/json

{
  "name": "无权限测试"
}

### 36. 无token访问 - 删除标签（应该失败）
DELETE {{baseUrl}}/admin/meditation/tags/1

### 37. 无效token访问 - 获取标签列表（应该失败）
GET {{baseUrl}}/admin/meditation/tags
Authorization: Bearer invalid_token_here

### ========================================
### 边界条件测试
### ========================================

### 38. 创建标签 - 最大长度名称（64字符）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "1234567890123456789012345678901234567890123456789012345678901234"
}

### 39. 创建标签 - 超长名称（应该失败或截断）
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "12345678901234567890123456789012345678901234567890123456789012345"
}

### 40. 获取标签列表 - 极大页码
GET {{baseUrl}}/admin/meditation/tags?page=999999&limit=10
Authorization: Bearer {{adminToken}}

### ========================================
### 标签使用情况测试
### ========================================

### 41. 删除正在使用的标签（应该失败）
# 注意：这个测试需要先有内容使用了某个标签
DELETE {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}

### 42. 批量删除包含正在使用的标签
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": [1, 2, 3, 4, 5]
}

### ========================================
### 数据验证测试
### ========================================

### 43. 创建标签 - 包含emoji
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "冥想😌"
}

### 44. 创建标签 - 包含数字
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "5分钟冥想"
}

### 45. 创建标签 - 中英文混合
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Meditation冥想"
}

### 46. 更新标签 - 保持原名称不变
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "测试标签"
}

### ========================================
### 性能测试
### ========================================

### 47. 获取标签列表 - 最大分页限制
GET {{baseUrl}}/admin/meditation/tags?page=1&limit=1000
Authorization: Bearer {{adminToken}}

### 48. 批量删除标签 - 大量ID
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
}

### ========================================
### 搜索功能详细测试
### ========================================

### 49. 搜索标签 - 部分匹配
GET {{baseUrl}}/admin/meditation/tags?search=冥&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 50. 搜索标签 - 大小写测试
GET {{baseUrl}}/admin/meditation/tags?search=MEDITATION&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 51. 搜索标签 - 特殊字符搜索
GET {{baseUrl}}/admin/meditation/tags?search=&放松&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 52. 搜索标签 - 数字搜索
GET {{baseUrl}}/admin/meditation/tags?search=5&page=1&limit=10
Authorization: Bearer {{adminToken}}

### ========================================
### 错误处理测试
### ========================================

### 53. 创建标签 - 发送非JSON数据
POST {{baseUrl}}/admin/meditation/tags
Authorization: Bearer {{adminToken}}
Content-Type: text/plain

测试标签

### 54. 更新标签 - 发送非JSON数据
PUT {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}
Content-Type: text/plain

更新标签

### 55. 批量删除 - 发送非数组数据
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tag_ids": "1,2,3"
}
