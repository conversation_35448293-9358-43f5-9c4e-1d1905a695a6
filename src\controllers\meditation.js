import MeditationContent from '../models/MeditationContent.js'
import UserFavorite from '../models/UserFavorite.js'
import MeditationTag from '../models/MeditationTag.js'
import MeditationContentTag from '../models/MeditationContentTag.js'
import sequelize from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common.js'

const { Op } = sequelize

export default class MeditationController {
  /**
   * @swagger
   * /meditation/list:
   *   get:
   *     tags:
   *       - 冥想模块
   *     summary: 获取冥想内容列表
   *     description: 获取冥想内容列表，支持分页和筛选
   *     parameters:
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [audio, video, text]
   *         description: 内容类型
   *       - in: query
   *         name: sub_type
   *         schema:
   *           type: string
   *         description: 子类型
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: parent_id
   *         schema:
   *           type: integer
   *         description: 父级内容ID
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/PaginatedResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getList(ctx) {
    const {
      type,
      sub_type,
      search,
      parent_id
    } = ctx.query

    try {
      const { pageNum: currentPageNum, pageSize: currentPageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      // 只显示已发布的内容
      whereCondition.status = 'published'

      if (type) whereCondition.type = type
      if (sub_type) whereCondition.sub_type = sub_type
      if (parent_id) whereCondition.parent_id = parent_id

      if (search) {
        whereCondition[Op.or] = [
          { title: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
          { tags_text: { [Op.like]: `%${search}%` } }
        ]
      }

      const contents = await MeditationContent.findAndCountAll({
        where: whereCondition,
        limit: currentPageSize,
        offset: offset,
        order: [['created_at', 'DESC']],
        distinct: true, // 确保count计算正确
        include: [{
          model: MeditationContent,
          as: 'parent',
          attributes: ['id', 'title']
        }]
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(contents.rows, contents.count, currentPageNum, currentPageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取冥想内容失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /meditation/{id}:
   *   get:
   *     tags:
   *       - 冥想模块
   *     summary: 获取冥想内容详情
   *     description: 获取指定ID的冥想内容详情
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 冥想内容ID
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   allOf:
   *                     - $ref: '#/components/schemas/MeditationContent'
   *                     - type: object
   *                       properties:
   *                         is_favorited:
   *                           type: boolean
   *                           description: 是否已收藏
   *                         parent:
   *                           $ref: '#/components/schemas/MeditationContent'
   *                         children:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/MeditationContent'
   *       404:
   *         description: 冥想内容不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getDetail(ctx) {
    const { id } = ctx.params
    const userId = ctx.state.user?.id

    try {
      const content = await MeditationContent.findOne({
        where: {
          id: id,
          status: 'published' // 只允许查看已发布的内容
        },
        include: [
          {
            model: MeditationContent,
            as: 'parent',
            attributes: ['id', 'title']
          },
          {
            model: MeditationContent,
            as: 'children',
            attributes: ['id', 'title', 'duration', 'cover_url'],
            where: { status: 'published' }, // 子内容也只显示已发布的
            required: false
          }
        ]
      })

      if (!content) {
        ctx.body = {
          code: 404,
          message: '冥想内容不存在或未发布'
        }
        return
      }

      // 检查用户是否收藏了该内容
      let is_favorited = false
      if (userId) {
        const favorite = await UserFavorite.findOne({
          where: { user_id: userId, meditation_id: id }
        })
        is_favorited = !!favorite
      }

      ctx.body = {
        code: 200,
        data: {
          ...content.toJSON(),
          is_favorited
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取冥想内容详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /meditation/{id}/favorite:
   *   post:
   *     tags:
   *       - 冥想模块
   *     summary: 收藏/取消收藏冥想内容
   *     description: 切换指定冥想内容的收藏状态
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 冥想内容ID
   *     responses:
   *       200:
   *         description: 操作成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "收藏成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     is_favorited:
   *                       type: boolean
   *                       description: 收藏状态
   *       404:
   *         description: 冥想内容不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async toggleFavorite(ctx) {
    const { id } = ctx.params
    const userId = ctx.state.user.id

    try {
      const content = await MeditationContent.findOne({
        where: {
          id: id,
          status: 'published' // 只允许收藏已发布的内容
        }
      })
      if (!content) {
        ctx.body = {
          code: 404,
          message: '冥想内容不存在或未发布'
        }
        return
      }

      const existingFavorite = await UserFavorite.findOne({
        where: { user_id: userId, meditation_id: id }
      })

      if (existingFavorite) {
        // 取消收藏
        await existingFavorite.destroy()
        await content.decrement('favorite_count')
        
        ctx.body = {
          code: 200,
          message: '取消收藏成功',
          data: { is_favorited: false }
        }
      } else {
        // 添加收藏
        await UserFavorite.create({
          user_id: userId,
          meditation_id: id
        })
        await content.increment('favorite_count')
        
        ctx.body = {
          code: 200,
          message: '收藏成功',
          data: { is_favorited: true }
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '操作失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /meditation/search:
   *   get:
   *     tags:
   *       - 冥想模块
   *     summary: 搜索冥想内容
   *     description: 根据关键词搜索冥想内容，支持标题、描述、标签搜索
   *     parameters:
   *       - in: query
   *         name: keyword
   *         required: true
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [audio, video, text]
   *         description: 内容类型筛选
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 搜索成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/PaginatedResponse'
   *       400:
   *         description: 搜索关键词不能为空
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async search(ctx) {
    const { keyword, type } = ctx.query

    if (!keyword) {
      ctx.body = {
        code: 400,
        message: '搜索关键词不能为空'
      }
      return
    }

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {
        status: 'published', // 只搜索已发布的内容
        [Op.or]: [
          { title: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } },
          { tags_text: { [Op.like]: `%${keyword}%` } }
        ]
      }

      if (type) {
        whereCondition.type = type
      }

      const contents = await MeditationContent.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['favorite_count', 'DESC'], ['created_at', 'DESC']]
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(contents.rows, contents.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '搜索失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /meditation/tags:
   *   get:
   *     tags:
   *       - 冥想模块
   *     summary: 获取冥想标签列表
   *     description: 获取所有可用的冥想标签
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: integer
   *                         description: 标签ID
   *                       name:
   *                         type: string
   *                         description: 标签名称
   *                       created_at:
   *                         type: string
   *                         format: date-time
   *                         description: 创建时间
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getTags(ctx) {
    try {
      console.log('开始获取标签列表...')
      console.log('MeditationTag model:', MeditationTag)

      const tags = await MeditationTag.findAll({
        where: { is_enabled: true }, // 只返回启用的标签
        order: [['created_at', 'DESC']]
      })

      console.log('获取到的启用标签数量:', tags.length)

      ctx.body = {
        code: 200,
        data: tags
      }
    } catch (error) {
      console.error('获取标签失败:', error)
      ctx.body = {
        code: 500,
        message: '获取标签失败',
        error: error.message
      }
    }
  }
}